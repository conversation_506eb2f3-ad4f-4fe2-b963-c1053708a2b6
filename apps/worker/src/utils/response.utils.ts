import { Response } from "express";
import { ApiResponse } from "../interfaces/api";

export class ResponseUtils {
  /**
   * Send successful response
   */
  static success<T>(
    res: Response,
    data: T,
    message?: string,
    count?: number
  ): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message: message || "Request successful",
      timestamp: new Date().toISOString(),
      count,
    };

    return res.status(200).json(response);
  }

  /**
   * Send created response (201)
   */
  static created<T>(res: Response, data: T, message?: string): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message: message || "Resource created successfully",
      timestamp: new Date().toISOString(),
    };

    return res.status(201).json(response);
  }

  /**
   * Send bad request error (400)
   */
  static badRequest(res: Response, error: string, message?: string): Response {
    const response: ApiResponse<null> = {
      success: false,
      error,
      message: message || "Bad request",
      timestamp: new Date().toISOString(),
    };

    return res.status(400).json(response);
  }

  /**
   * Send not found error (404)
   */
  static notFound(res: Response, error: string, message?: string): Response {
    const response: ApiResponse<null> = {
      success: false,
      error,
      message: message || "Resource not found",
      timestamp: new Date().toISOString(),
    };

    return res.status(404).json(response);
  }

  /**
   * Send unauthorized error (401)
   */
  static unauthorized(
    res: Response,
    error: string,
    message?: string
  ): Response {
    const response: ApiResponse<null> = {
      success: false,
      error,
      message: message || "Unauthorized",
      timestamp: new Date().toISOString(),
    };

    return res.status(401).json(response);
  }

  /**
   * Send too many requests error (429)
   */
  static tooManyRequests(
    res: Response,
    error: string,
    message?: string
  ): Response {
    const response: ApiResponse<null> = {
      success: false,
      error,
      message: message || "Too many requests",
      timestamp: new Date().toISOString(),
    };

    return res.status(429).json(response);
  }

  /**
   * Send service unavailable error (503)
   */
  static serviceUnavailable(
    res: Response,
    error: string,
    message?: string
  ): Response {
    const response: ApiResponse<null> = {
      success: false,
      error,
      message: message || "Service unavailable",
      timestamp: new Date().toISOString(),
    };

    return res.status(503).json(response);
  }

  /**
   * Send internal server error (500)
   */
  static internalError(
    res: Response,
    error: string,
    message?: string
  ): Response {
    const response: ApiResponse<null> = {
      success: false,
      error,
      message: message || "Internal server error",
      timestamp: new Date().toISOString(),
    };

    return res.status(500).json(response);
  }

  /**
   * Send validation error (422)
   */
  static validationError(
    res: Response,
    errors: any[],
    message?: string
  ): Response {
    const response = {
      success: false,
      error: "Validation failed",
      message: message || "Request validation failed",
      timestamp: new Date().toISOString(),
      details: errors,
    };

    return res.status(422).json(response);
  }

  /**
   * Send no content response (204)
   */
  static noContent(res: Response): Response {
    return res.status(204).send();
  }

  /**
   * Handle async errors in route handlers
   */
  static asyncHandler(fn: Function) {
    return (req: any, res: any, next: any) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Format error for logging
   */
  static formatError(error: any): string {
    if (error instanceof Error) {
      return `${error.name}: ${error.message}`;
    }
    return String(error);
  }

  /**
   * Log and send error response
   */
  static handleError(res: Response, error: any, context: string): Response {
    const errorMessage = this.formatError(error);
    console.error(`Error in ${context}:`, errorMessage);

    // Don't expose internal error details in production
    const isProduction = process.env.NODE_ENV === "production";
    const publicError = isProduction
      ? "An internal error occurred"
      : errorMessage;

    return this.internalError(res, publicError, `Error in ${context}`);
  }

  /**
   * Paginate results
   */
  static paginate<T>(
    data: T[],
    page: number = 1,
    limit: number = 100
  ): {
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  } {
    const offset = (page - 1) * limit;
    const paginatedData = data.slice(offset, offset + limit);

    return {
      data: paginatedData,
      pagination: {
        page,
        limit,
        total: data.length,
        totalPages: Math.ceil(data.length / limit),
        hasNext: offset + limit < data.length,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Format date for response
   */
  static formatDate(date: Date): string {
    return date.toISOString();
  }

  /**
   * Sanitize data for response (remove sensitive fields)
   */
  static sanitizeData<T>(data: T, fieldsToRemove: string[] = []): T {
    if (!data || typeof data !== "object") {
      return data;
    }

    const sanitized = { ...data } as any;
    fieldsToRemove.forEach((field) => {
      delete sanitized[field];
    });

    return sanitized;
  }

  /**
   * Create standardized error response for different error types
   */
  static createErrorResponse(error: any): {
    status: number;
    response: ApiResponse<null>;
  } {
    let status = 500;
    let errorMessage = "Internal server error";
    let message = "An unexpected error occurred";

    if (error.name === "ValidationError") {
      status = 400;
      errorMessage = "Validation failed";
      message = "Request validation failed";
    } else if (error.name === "CastError") {
      status = 400;
      errorMessage = "Invalid data format";
      message = "Invalid data format provided";
    } else if (error.code === 11000) {
      status = 409;
      errorMessage = "Duplicate entry";
      message = "Resource already exists";
    } else if (error.name === "MongoError") {
      status = 503;
      errorMessage = "Database error";
      message = "Database service unavailable";
    }

    const response: ApiResponse<null> = {
      success: false,
      error: errorMessage,
      message,
      timestamp: new Date().toISOString(),
    };

    return { status, response };
  }
}
