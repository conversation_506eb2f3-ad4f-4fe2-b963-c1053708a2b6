import { Request, Response } from "express";
import { ResponseUtils } from "../utils/response.utils";
import { KlineService } from "../services/kline.service";
import {
  KlineRequest,
  KlineDataResponse,
} from "../interfaces/api";

/**
 * Controller for K-line (OHLC) data endpoints
 * 
 * This controller handles:
 * - K-line data retrieval from Quidax API
 * - Request validation and parameter processing
 * - Response formatting and error handling
 * - Comprehensive logging and monitoring
 */
export class KlineController {
  private klineService: KlineService;

  constructor() {
    this.klineService = new KlineService();
  }

  /**
   * Get K-line (OHLC) data for a specific market
   * 
   * GET /api/market-data/kline/:marketId
   * 
   * @param req - Express request object with validated parameters
   * @param res - Express response object
   * 
   * Query Parameters:
   * - timestamp (optional): Unix timestamp in seconds - only return data after this time
   * - period (optional): Time period in minutes (1, 5, 15, 30, 60, 120, 240, 360, 720, 1440, 4320, 10080)
   * - limit (optional): Maximum number of data points to return (1-10000, default: 30)
   * 
   * Path Parameters:
   * - marketId (required): Market identifier (e.g., btc_ngn, eth_usdt)
   * 
   * Response Format:
   * {
   *   "success": true,
   *   "data": {
   *     "marketId": "btc_ngn",
   *     "data": [
   *       {
   *         "timestamp": 1750748940,
   *         "open": 105756.97,
   *         "high": 105756.97,
   *         "low": 105756.97,
   *         "close": 105756.97,
   *         "volume": 0
   *       }
   *     ],
   *     "count": 1,
   *     "parameters": {
   *       "timestamp": 1750748940,
   *       "period": 1,
   *       "limit": 30
   *     }
   *   },
   *   "message": "K-line data retrieved successfully",
   *   "timestamp": "2024-01-01T00:00:00.000Z"
   * }
   */
  getKlineData = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      // Extract validated parameters from middleware
      const { marketId, timestamp, period, limit } = req.validatedData as {
        marketId: string;
        timestamp?: number;
        period: number;
        limit: number;
      };

      console.log(`Fetching K-line data for market: ${marketId}, period: ${period}, limit: ${limit}${timestamp ? `, timestamp: ${timestamp}` : ''}`);

      // Prepare request parameters for the service
      const klineRequest: KlineRequest = {
        marketId,
        timestamp,
        period,
        limit,
      };

      // Fetch K-line data from Quidax API
      const klineData = await this.klineService.fetchKlineData(klineRequest);

      // Prepare response data
      const response: KlineDataResponse = {
        marketId,
        data: klineData,
        count: klineData.length,
        parameters: {
          timestamp,
          period,
          limit,
        },
      };

      // Log successful retrieval
      console.log(`Successfully retrieved ${klineData.length} K-line data points for market ${marketId}`);

      // Return successful response
      return ResponseUtils.success(
        res,
        response,
        "K-line data retrieved successfully",
        klineData.length
      );

    } catch (error) {
      // Enhanced error logging with context
      const { marketId } = req.validatedData || {};
      console.error(`Error fetching K-line data for market ${marketId}:`, error);

      // Handle specific error types with appropriate HTTP status codes
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase();

        if (errorMessage.includes("authentication") || errorMessage.includes("api key")) {
          return ResponseUtils.unauthorized(res, "Authentication failed");
        } else if (errorMessage.includes("market not found") || errorMessage.includes("404")) {
          return ResponseUtils.notFound(res, `Market '${marketId}' not found`);
        } else if (errorMessage.includes("rate limit")) {
          return ResponseUtils.tooManyRequests(res, "Rate limit exceeded");
        } else if (errorMessage.includes("timeout")) {
          return ResponseUtils.serviceUnavailable(res, "External API timeout");
        } else if (errorMessage.includes("network") || errorMessage.includes("connect")) {
          return ResponseUtils.serviceUnavailable(res, "External API unavailable");
        }
      }

      // Default to internal server error for unexpected errors
      return ResponseUtils.handleError(res, error, "getKlineData");
    }
  });

  /**
   * Get supported K-line periods
   * 
   * GET /api/market-data/kline/periods
   * 
   * @param req - Express request object
   * @param res - Express response object
   * 
   * Response Format:
   * {
   *   "success": true,
   *   "data": {
   *     "periods": [1, 5, 15, 30, 60, 120, 240, 360, 720, 1440, 4320, 10080],
   *     "descriptions": {
   *       "1": "1m",
   *       "5": "5m",
   *       "15": "15m",
   *       "30": "30m",
   *       "60": "1h",
   *       "120": "2h",
   *       "240": "4h",
   *       "360": "6h",
   *       "720": "12h",
   *       "1440": "1d",
   *       "4320": "3d",
   *       "10080": "7d"
   *     }
   *   },
   *   "message": "Supported K-line periods retrieved successfully",
   *   "timestamp": "2024-01-01T00:00:00.000Z"
   * }
   */
  getSupportedPeriods = ResponseUtils.asyncHandler(async (req: Request, res: Response) => {
    try {
      const periods = KlineService.getSupportedPeriods();
      
      // Create descriptions for each period
      const descriptions: Record<string, string> = {};
      periods.forEach(period => {
        descriptions[period.toString()] = KlineService.formatPeriod(period);
      });

      const response = {
        periods,
        descriptions,
        count: periods.length,
      };

      return ResponseUtils.success(
        res,
        response,
        "Supported K-line periods retrieved successfully"
      );

    } catch (error) {
      return ResponseUtils.handleError(res, error, "getSupportedPeriods");
    }
  });
}
