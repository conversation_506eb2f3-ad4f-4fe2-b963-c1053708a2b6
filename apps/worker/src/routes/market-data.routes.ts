import { Router } from "express";
import { MarketDataController } from "../controllers/market-data.controller";
import { KlineController } from "../controllers/kline.controller";
import { ValidationMiddleware } from "../middleware/validation.middleware";

const router = Router();
const marketDataController = new MarketDataController();
const klineController = new KlineController();

/**
 * @route GET /api/market-data/health
 * @desc Enhanced health check endpoint
 * @access Public
 */
router.get("/health", marketDataController.getHealthCheck);

/**
 * @route GET /api/market-data/pairs
 * @desc Get all available currency pairs
 * @access Public
 */
router.get("/pairs", marketDataController.getCurrencyPairs);

/**
 * @route GET /api/market-data/latest/:currencyPair
 * @desc Get latest market data for a specific currency pair
 * @param {string} currencyPair - Currency pair (e.g., BTC/USDT)
 * @access Public
 */
router.get(
  "/latest/:currencyPair",
  ValidationMiddleware.validateCurrencyPairParam,
  marketDataController.getLatestData
);

/**
 * @route GET /api/market-data/historical
 * @desc Get historical market data
 * @query {string} currencyPair - Currency pair (required)
 * @query {string} start - Start date in ISO format (required)
 * @query {string} end - End date in ISO format (required)
 * @query {number} limit - Maximum number of records (optional, max 10000)
 * @access Public
 */
router.get(
  "/historical",
  ValidationMiddleware.validateHistoricalDataRequest,
  marketDataController.getHistoricalData
);

/**
 * @route GET /api/market-data/aggregated
 * @desc Get aggregated market data with OHLC-like statistics
 * @query {string} currencyPair - Currency pair (required)
 * @query {string} start - Start date in ISO format (required)
 * @query {string} end - End date in ISO format (required)
 * @query {string} interval - Aggregation interval: minute, hour, day, week, month (required)
 * @access Public
 */
router.get(
  "/aggregated",
  ValidationMiddleware.validateAggregatedDataRequest,
  marketDataController.getAggregatedData
);

/**
 * @route GET /api/market-data/range
 * @desc Get market data by date range with flexible filtering
 * @query {string} start - Start date in ISO format (required)
 * @query {string} end - End date in ISO format (required)
 * @query {string} currencyPair - Currency pair (optional, if not provided returns all pairs)
 * @query {number} limit - Maximum number of records (optional)
 * @query {string} sortOrder - Sort order: asc or desc (optional, default: asc)
 * @access Public
 */
router.get("/range", marketDataController.getDataByRange);

/**
 * @route GET /api/market-data/statistics/:currencyPair
 * @desc Get market statistics for a currency pair
 * @param {string} currencyPair - Currency pair (e.g., BTC/USDT)
 * @query {string} period - Time period: 24h, 7d, 30d (optional, default: 24h)
 * @access Public
 */
router.get(
  "/statistics/:currencyPair",
  ValidationMiddleware.validateCurrencyPairParam,
  marketDataController.getMarketStatistics
);

/**
 * @route GET /api/market-data/kline/periods
 * @desc Get supported K-line periods
 * @access Public
 */
router.get("/kline/periods", klineController.getSupportedPeriods);

/**
 * @route GET /api/market-data/kline/:marketId
 * @desc Get K-line (OHLC) data from Quidax API
 * @param {string} marketId - Market identifier (e.g., btc_ngn, eth_usdt)
 * @query {number} timestamp - Unix timestamp in seconds (optional)
 * @query {number} period - Time period in minutes: 1, 5, 15, 30, 60, 120, 240, 360, 720, 1440, 4320, 10080 (optional, default: 1)
 * @query {number} limit - Maximum number of data points (1-10000, optional, default: 30)
 * @access Public
 */
router.get(
  "/kline/:marketId",
  ValidationMiddleware.validateKlineParams,
  klineController.getKlineData
);

export { router as marketDataRoutes };
