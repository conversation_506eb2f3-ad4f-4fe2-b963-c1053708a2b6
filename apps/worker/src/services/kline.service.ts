import axios, { AxiosResponse } from "axios";
import { workerConfig as config } from "@crypto-watcher/shared";
import {
  QuidaxKlineResponse,
  KlineDataPoint,
  KlineRequest,
} from "../interfaces/api";

/**
 * Service for fetching K-line (OHLC) data from Quidax API
 *
 * This service handles:
 * - HTTP requests to Quidax K-line API endpoint
 * - Authentication with API key
 * - Response transformation from raw array format to structured objects
 * - Error handling for external API failures
 * - Request parameter validation and formatting
 */
export class KlineService {
  private readonly BASE_URL = config.QUIDAX_BASE_URL;
  private readonly API_KEY = config.API_KEY;

  /**
   * Fetch K-line data from Quidax API
   *
   * @param params - K-line request parameters
   * @returns Promise<KlineDataPoint[]> - Array of structured K-line data points
   * @throws Error - When API request fails or returns invalid data
   */
  async fetchKlineData(params: KlineRequest): Promise<KlineDataPoint[]> {
    try {
      // Build query parameters for Quidax API
      const queryParams = new URLSearchParams();

      if (params.timestamp !== undefined) {
        queryParams.append("timestamp", params.timestamp.toString());
      }

      if (params.period !== undefined) {
        queryParams.append("period", params.period.toString());
      }

      if (params.limit !== undefined) {
        queryParams.append("limit", params.limit.toString());
      }

      // Construct the full URL
      const url = `${this.BASE_URL}/markets/${params.marketId}/k`;
      const fullUrl = queryParams.toString()
        ? `${url}?${queryParams.toString()}`
        : url;

      console.log(`Fetching K-line data from: ${fullUrl}`);

      // Make HTTP request to Quidax API
      const response: AxiosResponse<QuidaxKlineResponse> = await axios.get(
        fullUrl,
        {
          headers: {
            Authorization: `Bearer ${this.API_KEY}`,
            "Content-Type": "application/json",
          },
          timeout: 10000, // 10 second timeout
        }
      );

      // Validate response structure
      if (!response.data || typeof response.data !== "object") {
        console.error(
          "Invalid response from Quidax API:",
          JSON.stringify(response.data, null, 2)
        );
        throw new Error("Invalid response format from Quidax API");
      }

      console.log(
        "Quidax API response:",
        JSON.stringify(response.data, null, 2)
      );

      const { status, message, data } = response.data;

      // Check if API returned success status
      if (status !== "success") {
        throw new Error(`Quidax API error: ${message || "Unknown error"}`);
      }

      // Validate data array
      if (!Array.isArray(data)) {
        throw new Error(
          "Invalid data format: expected array of K-line data points"
        );
      }

      // Transform raw array data to structured objects
      const klineData: KlineDataPoint[] = data.map((item, index) => {
        if (!Array.isArray(item) || item.length !== 6) {
          throw new Error(
            `Invalid K-line data point at index ${index}: expected array of 6 elements [timestamp, open, high, low, close, volume]`
          );
        }

        const [timestamp, open, high, low, close, volume] = item;

        // Validate each field is a number
        if (
          typeof timestamp !== "number" ||
          typeof open !== "number" ||
          typeof high !== "number" ||
          typeof low !== "number" ||
          typeof close !== "number" ||
          typeof volume !== "number"
        ) {
          throw new Error(
            `Invalid K-line data types at index ${index}: all values must be numbers`
          );
        }

        // Validate OHLC relationships
        if (high < Math.max(open, close) || low > Math.min(open, close)) {
          console.warn(
            `Suspicious OHLC values at index ${index}: high=${high}, low=${low}, open=${open}, close=${close}`
          );
        }

        return {
          timestamp,
          open,
          high,
          low,
          close,
          volume,
        };
      });

      console.log(
        `Successfully fetched ${klineData.length} K-line data points for market ${params.marketId}`
      );
      return klineData;
    } catch (error) {
      // Enhanced error handling with context
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const statusText = error.response?.statusText;
        const responseData = error.response?.data;

        if (status === 401) {
          throw new Error("Authentication failed: Invalid API key");
        } else if (status === 404) {
          throw new Error(`Market not found: ${params.marketId}`);
        } else if (status === 429) {
          throw new Error(
            "Rate limit exceeded: Too many requests to Quidax API"
          );
        } else if (status && status >= 500) {
          throw new Error(`Quidax API server error (${status}): ${statusText}`);
        } else if (error.code === "ECONNABORTED") {
          throw new Error(
            "Request timeout: Quidax API did not respond within 10 seconds"
          );
        } else if (
          error.code === "ENOTFOUND" ||
          error.code === "ECONNREFUSED"
        ) {
          throw new Error("Network error: Unable to connect to Quidax API");
        } else {
          const errorMessage =
            responseData?.message || error.message || "Unknown API error";
          throw new Error(`Quidax API request failed: ${errorMessage}`);
        }
      } else if (error instanceof Error) {
        // Re-throw our custom errors
        throw error;
      } else {
        throw new Error(
          `Unexpected error fetching K-line data: ${String(error)}`
        );
      }
    }
  }

  /**
   * Validate K-line data integrity
   *
   * @param data - Array of K-line data points
   * @returns boolean - True if data passes validation
   */
  private validateKlineData(data: KlineDataPoint[]): boolean {
    if (!Array.isArray(data) || data.length === 0) {
      return false;
    }

    // Check if timestamps are in ascending order
    for (let i = 1; i < data.length; i++) {
      if (data[i].timestamp <= data[i - 1].timestamp) {
        console.warn(
          `K-line data timestamps not in ascending order at index ${i}`
        );
        return false;
      }
    }

    return true;
  }

  /**
   * Get supported periods for K-line data
   *
   * @returns number[] - Array of supported period values in minutes
   */
  static getSupportedPeriods(): number[] {
    return [1, 5, 15, 30, 60, 120, 240, 360, 720, 1440, 4320, 10080];
  }

  /**
   * Convert period to human-readable format
   *
   * @param period - Period in minutes
   * @returns string - Human-readable period description
   */
  static formatPeriod(period: number): string {
    if (period < 60) {
      return `${period}m`;
    } else if (period < 1440) {
      return `${period / 60}h`;
    } else {
      return `${period / 1440}d`;
    }
  }
}
