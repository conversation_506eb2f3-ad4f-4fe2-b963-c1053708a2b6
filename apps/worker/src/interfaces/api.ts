import {
  CombinedMarketData,
  AggregatedMarketData,
} from "@crypto-watcher/shared";

// Request DTOs
export interface HistoricalDataRequest {
  currencyPair: string;
  start: string; // ISO date string
  end: string; // ISO date string
  limit?: number;
}

export interface AggregatedDataRequest {
  currencyPair: string;
  start: string; // ISO date string
  end: string; // ISO date string
  interval: "minute" | "hour" | "day" | "week" | "month";
}

export interface DateRangeRequest {
  currencyPair?: string;
  start: string; // ISO date string
  end: string; // ISO date string
  limit?: number;
  sortOrder?: "asc" | "desc";
}

export interface StatisticsRequest {
  currencyPair: string;
  period?: "24h" | "7d" | "30d";
}

// K-line (OHLC) Request DTOs
export interface KlineRequest {
  marketId: string;
  timestamp?: number; // Unix timestamp in seconds
  period?: number; // Time period: 1, 5, 15, 30, 60, 120, 240, 360, 720, 1440, 4320, 10080
  limit?: number; // Range: 1-10000, default: 30
}

// Quidax K-line API Response DTOs
export interface QuidaxKlineDataPoint {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface QuidaxKlineResponse {
  status: string;
  message: string;
  data: number[][]; // Array of [timestamp, open, high, low, close, volume]
}

// Response DTOs
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  count?: number;
}

export interface HistoricalDataResponse {
  currencyPair: string;
  data: CombinedMarketData[];
  count: number;
  dateRange: {
    start: string;
    end: string;
  };
}

export interface AggregatedDataResponse {
  currencyPair: string;
  interval: string;
  data: AggregatedMarketData[];
  count: number;
  dateRange: {
    start: string;
    end: string;
  };
}

export interface LatestDataResponse {
  currencyPair: string;
  data: CombinedMarketData | null;
  timestamp: string;
}

export interface MarketStatistics {
  currencyPair: string;
  period: string;
  totalRecords: number;
  priceStats: {
    current: number;
    min: number;
    max: number;
    average: number;
    change: number;
    changePercent: number;
  };
  volumeStats: {
    total: number;
    average: number;
    min: number;
    max: number;
  };
  dateRange: {
    start: string;
    end: string;
  };
}

export interface CurrencyPairsResponse {
  pairs: string[];
  count: number;
  lastUpdated: string;
}

export interface HealthCheckResponse {
  status: "healthy" | "unhealthy";
  service: string;
  timestamp: string;
  uptime: number;
  database: {
    mongodb: "connected" | "disconnected";
    redis: "connected" | "disconnected";
  };
  version: string;
}

// K-line Response DTOs
export interface KlineDataPoint {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface KlineDataResponse {
  marketId: string;
  data: KlineDataPoint[];
  count: number;
  parameters: {
    timestamp?: number;
    period: number;
    limit: number;
  };
}

// Validation interfaces
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Query parameter interfaces
export interface HistoricalDataQuery extends PaginationParams, SortParams {
  currencyPair: string;
  start: string;
  end: string;
}

export interface AggregatedDataQuery {
  currencyPair: string;
  start: string;
  end: string;
  interval: "minute" | "hour" | "day" | "week" | "month";
}

export interface DateRangeQuery extends PaginationParams, SortParams {
  currencyPair?: string;
  start: string;
  end: string;
}
