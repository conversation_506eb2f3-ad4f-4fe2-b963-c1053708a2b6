import { Request, Response, NextFunction } from "express";
import { ValidationError } from "../interfaces/api";

export class ValidationMiddleware {
  /**
   * Validate currency pair format
   */
  static validateCurrencyPair(currencyPair: string): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!currencyPair) {
      errors.push({
        field: "currencyPair",
        message: "Currency pair is required",
      });
      return errors;
    }

    if (typeof currencyPair !== "string") {
      errors.push({
        field: "currencyPair",
        message: "Currency pair must be a string",
        value: currencyPair,
      });
    }

    // Basic format validation (e.g., btcusdt, ethusdt, BTC/USDT, ETH_USDT)
    // Must be either: lowercase letters 6-20 chars, or uppercase with separator
    const currencyPairRegex = /^([A-Z]{2,10}[/_][A-Z]{2,10}|[a-z]{6,20})$/;
    if (!currencyPairRegex.test(currencyPair)) {
      errors.push({
        field: "currencyPair",
        message:
          "Invalid currency pair format. Expected format: btcusdt (6-20 lowercase letters), BTC/USDT, or BTC_USDT",
        value: currencyPair,
      });
    }

    return errors;
  }

  /**
   * Validate date string and convert to Date object
   */
  static validateDate(
    dateString: string,
    fieldName: string
  ): { errors: ValidationError[]; date?: Date } {
    const errors: ValidationError[] = [];

    if (!dateString) {
      errors.push({
        field: fieldName,
        message: `${fieldName} is required`,
      });
      return { errors };
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      errors.push({
        field: fieldName,
        message: `Invalid ${fieldName} format. Expected ISO date string`,
        value: dateString,
      });
      return { errors };
    }

    return { errors, date };
  }

  /**
   * Validate date range
   */
  static validateDateRange(
    start: string,
    end: string
  ): { errors: ValidationError[]; startDate?: Date; endDate?: Date } {
    const errors: ValidationError[] = [];

    const startValidation = this.validateDate(start, "start");
    const endValidation = this.validateDate(end, "end");

    errors.push(...startValidation.errors, ...endValidation.errors);

    if (errors.length > 0) {
      return { errors };
    }

    const startDate = startValidation.date!;
    const endDate = endValidation.date!;

    if (startDate >= endDate) {
      errors.push({
        field: "dateRange",
        message: "Start date must be before end date",
        value: { start, end },
      });
    }

    // Validate reasonable date range (not more than 1 year)
    const maxRangeMs = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
    if (endDate.getTime() - startDate.getTime() > maxRangeMs) {
      errors.push({
        field: "dateRange",
        message: "Date range cannot exceed 1 year",
        value: { start, end },
      });
    }

    return { errors, startDate, endDate };
  }

  /**
   * Validate aggregation interval
   */
  static validateInterval(interval: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const validIntervals = ["minute", "hour", "day", "week", "month"];

    if (!interval) {
      errors.push({
        field: "interval",
        message: "Interval is required",
      });
      return errors;
    }

    if (!validIntervals.includes(interval)) {
      errors.push({
        field: "interval",
        message: `Invalid interval. Must be one of: ${validIntervals.join(
          ", "
        )}`,
        value: interval,
      });
    }

    return errors;
  }

  /**
   * Validate limit parameter
   */
  static validateLimit(limit: any): ValidationError[] {
    const errors: ValidationError[] = [];

    if (limit !== undefined) {
      const numLimit = parseInt(limit, 10);
      if (isNaN(numLimit) || numLimit <= 0) {
        errors.push({
          field: "limit",
          message: "Limit must be a positive integer",
          value: limit,
        });
      } else if (numLimit > 10000) {
        errors.push({
          field: "limit",
          message: "Limit cannot exceed 10,000 records",
          value: limit,
        });
      }
    }

    return errors;
  }

  /**
   * Validate pagination parameters
   */
  static validatePagination(page: any, limit: any): ValidationError[] {
    const errors: ValidationError[] = [];

    if (page !== undefined) {
      const numPage = parseInt(page, 10);
      if (isNaN(numPage) || numPage < 1) {
        errors.push({
          field: "page",
          message: "Page must be a positive integer starting from 1",
          value: page,
        });
      }
    }

    errors.push(...this.validateLimit(limit));

    return errors;
  }

  /**
   * Middleware for validating historical data requests
   */
  static validateHistoricalDataRequest(
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    const { currencyPair, start, end, limit } = req.query;
    const errors: ValidationError[] = [];

    // Validate currency pair
    errors.push(
      ...ValidationMiddleware.validateCurrencyPair(currencyPair as string)
    );

    // Validate date range
    const dateRangeValidation = ValidationMiddleware.validateDateRange(
      start as string,
      end as string
    );
    errors.push(...dateRangeValidation.errors);

    // Validate limit
    errors.push(...ValidationMiddleware.validateLimit(limit));

    if (errors.length > 0) {
      res.status(400).json({
        success: false,
        error: "Validation failed",
        message: "Invalid request parameters",
        details: errors,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Attach validated data to request
    req.validatedData = {
      currencyPair: currencyPair as string,
      startDate: dateRangeValidation.startDate!,
      endDate: dateRangeValidation.endDate!,
      limit: limit ? parseInt(limit as string, 10) : undefined,
    };

    next();
  }

  /**
   * Middleware for validating aggregated data requests
   */
  static validateAggregatedDataRequest(
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    const { currencyPair, start, end, interval } = req.query;
    const errors: ValidationError[] = [];

    // Validate currency pair
    errors.push(
      ...ValidationMiddleware.validateCurrencyPair(currencyPair as string)
    );

    // Validate date range
    const dateRangeValidation = ValidationMiddleware.validateDateRange(
      start as string,
      end as string
    );
    errors.push(...dateRangeValidation.errors);

    // Validate interval
    errors.push(...ValidationMiddleware.validateInterval(interval as string));

    if (errors.length > 0) {
      res.status(400).json({
        success: false,
        error: "Validation failed",
        message: "Invalid request parameters",
        details: errors,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Attach validated data to request
    req.validatedData = {
      currencyPair: currencyPair as string,
      startDate: dateRangeValidation.startDate!,
      endDate: dateRangeValidation.endDate!,
      interval: interval as "minute" | "hour" | "day" | "week" | "month",
    };

    next();
  }

  /**
   * Middleware for validating currency pair parameter
   */
  static validateCurrencyPairParam(
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    const { currencyPair } = req.params;
    const errors = ValidationMiddleware.validateCurrencyPair(currencyPair);

    if (errors.length > 0) {
      res.status(400).json({
        success: false,
        error: "Validation failed",
        message: "Invalid currency pair",
        details: errors,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    next();
  }

  /**
   * Validate market ID for K-line endpoint
   */
  static validateMarketId(marketId: string): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!marketId) {
      errors.push({
        field: "marketId",
        message: "Market ID is required",
      });
      return errors;
    }

    if (typeof marketId !== "string") {
      errors.push({
        field: "marketId",
        message: "Market ID must be a string",
        value: marketId,
      });
    }

    // Market ID should be a valid trading pair format (e.g., btc_ngn, eth_usdt)
    const marketIdRegex = /^[a-z]{2,10}_[a-z]{2,10}$/;
    if (!marketIdRegex.test(marketId)) {
      errors.push({
        field: "marketId",
        message:
          "Invalid market ID format. Expected format: base_quote (e.g., btc_ngn, eth_usdt)",
        value: marketId,
      });
    }

    return errors;
  }

  /**
   * Validate K-line period parameter
   */
  static validateKlinePeriod(period?: string | number): ValidationError[] {
    const errors: ValidationError[] = [];

    if (period === undefined || period === null || period === "") {
      return errors; // Period is optional
    }

    const numericPeriod =
      typeof period === "string" ? parseInt(period, 10) : period;

    if (isNaN(numericPeriod)) {
      errors.push({
        field: "period",
        message: "Period must be a valid number",
        value: period,
      });
      return errors;
    }

    // Valid periods: 1, 5, 15, 30, 60, 120, 240, 360, 720, 1440, 4320, 10080
    const validPeriods = [
      1, 5, 15, 30, 60, 120, 240, 360, 720, 1440, 4320, 10080,
    ];
    if (!validPeriods.includes(numericPeriod)) {
      errors.push({
        field: "period",
        message: `Invalid period. Must be one of: ${validPeriods.join(", ")}`,
        value: period,
      });
    }

    return errors;
  }

  /**
   * Validate K-line limit parameter
   */
  static validateKlineLimit(limit?: string | number): ValidationError[] {
    const errors: ValidationError[] = [];

    if (limit === undefined || limit === null || limit === "") {
      return errors; // Limit is optional
    }

    const numericLimit =
      typeof limit === "string" ? parseInt(limit, 10) : limit;

    if (isNaN(numericLimit)) {
      errors.push({
        field: "limit",
        message: "Limit must be a valid number",
        value: limit,
      });
      return errors;
    }

    if (numericLimit < 1 || numericLimit > 10000) {
      errors.push({
        field: "limit",
        message: "Limit must be between 1 and 10000",
        value: limit,
      });
    }

    return errors;
  }

  /**
   * Validate K-line timestamp parameter
   */
  static validateKlineTimestamp(
    timestamp?: string | number
  ): ValidationError[] {
    const errors: ValidationError[] = [];

    if (timestamp === undefined || timestamp === null || timestamp === "") {
      return errors; // Timestamp is optional
    }

    const numericTimestamp =
      typeof timestamp === "string" ? parseInt(timestamp, 10) : timestamp;

    if (isNaN(numericTimestamp)) {
      errors.push({
        field: "timestamp",
        message: "Timestamp must be a valid number (Unix timestamp in seconds)",
        value: timestamp,
      });
      return errors;
    }

    // Validate timestamp is reasonable (not negative, not too far in future)
    const now = Math.floor(Date.now() / 1000);
    const oneYearFromNow = now + 365 * 24 * 60 * 60;

    if (numericTimestamp < 0) {
      errors.push({
        field: "timestamp",
        message: "Timestamp cannot be negative",
        value: timestamp,
      });
    } else if (numericTimestamp > oneYearFromNow) {
      errors.push({
        field: "timestamp",
        message: "Timestamp cannot be more than one year in the future",
        value: timestamp,
      });
    }

    return errors;
  }

  /**
   * Middleware for validating K-line endpoint parameters
   */
  static validateKlineParams(
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    const { marketId } = req.params;
    const { timestamp, period, limit } = req.query;

    const errors: ValidationError[] = [];

    // Validate market ID (required path parameter)
    errors.push(...ValidationMiddleware.validateMarketId(marketId));

    // Validate optional query parameters
    errors.push(
      ...ValidationMiddleware.validateKlineTimestamp(timestamp as string)
    );
    errors.push(...ValidationMiddleware.validateKlinePeriod(period as string));
    errors.push(...ValidationMiddleware.validateKlineLimit(limit as string));

    if (errors.length > 0) {
      res.status(400).json({
        success: false,
        error: "Validation failed",
        message: "Invalid K-line request parameters",
        details: errors,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Attach validated data to request
    req.validatedData = {
      marketId: marketId as string,
      timestamp: timestamp ? parseInt(timestamp as string, 10) : undefined,
      period: period ? parseInt(period as string, 10) : 1, // Default to 1
      limit: limit ? parseInt(limit as string, 10) : 30, // Default to 30
    };

    next();
  }
}

// Extend Express Request interface to include validated data
declare global {
  namespace Express {
    interface Request {
      validatedData?: any;
    }
  }
}
